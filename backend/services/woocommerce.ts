import WooCommerceRestApi from '@woocommerce/woocommerce-rest-api';
import { createModuleLogger } from '../infrastructure/logger';
import { WooCommerceCategory } from '@/sync/rza/export';

const logger = createModuleLogger('woocommerce');

export interface WooCommerceConfig {
  url: string;
  consumerKey: string;
  consumerSecret: string;
  version?: string;
}

export interface Product {
  id?: number;
  name: string;
  slug?: string;
  type?: string;
  status?: string;
  featured?: boolean;
  catalog_visibility?: string;
  description?: string;
  short_description?: string;
  sku?: string;
  price?: string;
  regular_price?: string;
  sale_price?: string;
  manage_stock?: boolean;
  stock_quantity?: number;
  stock_status?: string;
  categories?: Array<{ id: number; name?: string }>;
  images?: Array<{ src: string; alt?: string }>;
  weight?: string;
  dimensions?: {
    length?: string;
    width?: string;
    height?: string;
  };
  tax_status?: string;
  tax_class?: string;
  meta_data?: Array<{ key: string; value: string }>;
}

export interface MediaItem {
  id: number;
  date: string;
  date_gmt: string;
  guid: {
    rendered: string;
  };
  modified: string;
  modified_gmt: string;
  slug: string;
  status: string;
  type: string;
  link: string;
  title: {
    rendered: string;
  };
  author: number;
  comment_status: string;
  ping_status: string;
  template: string;
  meta: any[];
  description: {
    rendered: string;
  };
  caption: {
    rendered: string;
  };
  alt_text: string;
  media_type: string;
  mime_type: string;
  media_details: {
    width: number;
    height: number;
    file: string;
    sizes: any;
    image_meta: any;
  };
  source_url: string;
}

export interface Order {
  id: number;
  status: string;
  currency: string;
  total: string;
  date_created: string;
  date_modified: string;
  customer_id: number;
  billing: {
    first_name: string;
    last_name: string;
    email: string;
    phone?: string;
    address_1: string;
    address_2?: string;
    city: string;
    state: string;
    postcode: string;
    country: string;
  };
  shipping: {
    first_name: string;
    last_name: string;
    address_1: string;
    address_2?: string;
    city: string;
    state: string;
    postcode: string;
    country: string;
  };
  line_items: Array<{
    id: number;
    name: string;
    product_id: number;
    variation_id: number;
    quantity: number;
    price: string;
    total: string;
    sku: string;
  }>;
}

export class WooCommerceService {
  private api: WooCommerceRestApi;
  private mediaCache: {prefix: Record<string, MediaItem[]>} = {prefix: {}};

  public constructor(config: WooCommerceConfig) {
    logger.info("WooCommerce Config", config)
    this.api = new WooCommerceRestApi({
      url: config.url,
      consumerKey: config.consumerKey,
      consumerSecret: config.consumerSecret,
      version:  'wc/v3',
      queryStringAuth: true
    });

    logger.info('WooCommerce API client initialized', {
      url: config.url,
      version: 'wc/v3'
    });
  }

  public clearMediaCache() {
    this.mediaCache = {prefix: {}};
  }

  // Product methods
  public async getProducts(params: any = {}): Promise<Product[]> {
    try {
      const response = await this.api.get('products', params);
      logger.info(`Retrieved ${response.data.length} products`);
      return response.data;
    } catch (error) {
      logger.error('Failed to get products:', error);
      throw error;
    }
  }

  public async getProduct(id: number): Promise<Product> {
    try {
      const response = await this.api.get(`products/${id}`);
      return response.data;
    } catch (error) {
      logger.error(`Failed to get product ${id}:`, error);
      throw error;
    }
  }

  public async createProduct(product: Product): Promise<Product> {
    try {
      const response = await this.api.post('products', product);
      logger.info(`Created product: ${response.data.name} (ID: ${response.data.id})`);
      return response.data;
    } catch (error) {
      logger.error('Failed to create product:', error);
      throw error;
    }
  }

  public async updateProduct(id: number, product: Partial<Product>): Promise<Product> {
    try {
      const response = await this.api.put(`products/${id}`, product);
      logger.info(`Updated product ID: ${id}`);
      return response.data;
    } catch (error) {
      logger.error(`Failed to update product ${id}:`, error);
      throw error;
    }
  }

  public async deleteProduct(id: number): Promise<void> {
    try {
      await this.api.delete(`products/${id}`, { force: true });
      logger.info(`Deleted product ID: ${id}`);
    } catch (error) {
      logger.error(`Failed to delete product ${id}:`, error);
      throw error;
    }
  }

  // Order methods
  public async getOrders(params: any = {}): Promise<Order[]> {
    try {
      const response = await this.api.get('orders', params);
      logger.info(`Retrieved ${response.data.length} orders`);
      return response.data;
    } catch (error) {
      logger.error('Failed to get orders:', error);
      throw error;
    }
  }

  public async getOrder(id: number): Promise<Order> {
    try {
      const response = await this.api.get(`orders/${id}`);
      return response.data;
    } catch (error) {
      logger.error(`Failed to get order ${id}:`, error);
      throw error;
    }
  }

  public async updateOrderStatus(id: number, status: string): Promise<Order> {
    try {
      const response = await this.api.put(`orders/${id}`, { status });
      logger.info(`Updated order ${id} status to: ${status}`);
      return response.data;
    } catch (error) {
      logger.error(`Failed to update order ${id} status:`, error);
      throw error;
    }
  }

  // Categories
  public async getCategories(params: any = {}): Promise<WooCommerceCategory[]> {
    try {
      const response = await this.api.get('products/categories', params);
      logger.info(`Retrieved ${response.data.length} categories`);
      return response.data;
    } catch (error) {
      logger.error('Failed to get categories:', error);
      throw error;
    }
  }

  // Media Library methods
  public async getAllMediaByPrefix(prefix: string): Promise<MediaItem[]> {
    if(this.mediaCache.prefix[prefix]) {
      return this.mediaCache.prefix[prefix];
    }

    try {
      const response = await this.api.get('media', {
        per_page: 100,
        page: 1,
        search: prefix
      });
      this.mediaCache.prefix[prefix] = response.data;

      logger.info(`Retrieved ${response.data.length} media items`);
      return response.data;
    } catch (error) {
      logger.error('Failed to get media:', error);
      throw error;
    }
  }

  public async getMediaByNamePrefix(namePrefix: string): Promise<MediaItem[]> {
    try {
      if (!namePrefix || namePrefix.trim() === '') {
        logger.debug('Empty image prefix provided, returning empty array');
        return [];
      }

      logger.info(`Searching for media with name prefix: ${namePrefix}`);

      // Get all media items and filter by name prefix
      // WordPress doesn't have a direct search by filename, so we need to get all and filter
      const allMedia = await this.getAllMediaByPrefix(namePrefix);

      if (allMedia.length === 0) {
        logger.warn('No media items found in WordPress media library');
        return [];
      }

      const matchingMedia = allMedia.filter(media => {
        try {
          // Check if the media title or slug starts with the prefix
          const title = (media.title?.rendered || '').toLowerCase();
          const slug = (media.slug || '').toLowerCase();
          const prefix = namePrefix.toLowerCase();

          // Also check the filename from media_details if available
          const filename = (media.media_details?.file || '').toLowerCase();
          const filenameOnly = filename.split('/').pop() || '';

          return title.startsWith(prefix) ||
                 slug.startsWith(prefix) ||
                 filenameOnly.startsWith(prefix);
        } catch (filterError) {
          logger.warn(`Error filtering media item ${media.id}:`, filterError);
          return false;
        }
      });

      // Sort by name to ensure consistent ordering (first image will be main image)
      matchingMedia.sort((a, b) => {
        try {
          const aName = a.title?.rendered || a.slug || '';
          const bName = b.title?.rendered || b.slug || '';
          return aName.localeCompare(bName);
        } catch (sortError) {
          logger.warn('Error sorting media items:', sortError);
          return 0;
        }
      });

      logger.info(`Found ${matchingMedia.length} media items matching prefix: ${namePrefix}`);

      if (matchingMedia.length > 0) {
        logger.debug(`First matching image: ${matchingMedia[0]?.title?.rendered || matchingMedia[0]?.slug}`);
      }

      return matchingMedia;
    } catch (error) {
      logger.error(`Failed to get media by name prefix ${namePrefix}:`, error);
      // Return empty array instead of throwing to allow product sync to continue
      return [];
    }
  }

  // Health check
  public async healthCheck(): Promise<boolean> {
    try {
      await this.api.get('system_status');
      return true;
    } catch (error) {
      console.error('WooCommerce health check failed:', error);
      logger.error('WooCommerce health check failed:', error);
      return false;
    }
  }
}

