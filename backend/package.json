{"name": "backend", "version": "1.0.0", "description": "Backend service for WooCommerce sync", "main": "dist/server/index.js", "scripts": {"dev": "tsx watch --require reflect-metadata server/index.ts", "build": "tsc", "start": "node dist/server/index.js", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@types/multer": "^2.0.0", "@woocommerce/woocommerce-rest-api": "^1.0.1", "axios": "^1.6.2", "compression": "^1.7.4", "cors": "^2.8.5", "cron": "^3.1.6", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "multer": "^2.0.1", "reflect-metadata": "^0.2.2", "sqlite3": "^5.1.6", "typeorm": "^0.3.25", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "xml2js": "^0.6.2", "xmlbuilder2": "^3.1.1", "zod": "^3.22.4"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/node": "^20.19.9", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "tsx": "^4.6.2", "typescript": "^5.3.3"}}