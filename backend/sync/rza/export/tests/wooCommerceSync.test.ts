/**
 * Unit tests for WooCommerce sync utilities
 */

import { upsertProductsToWooCommerce } from '../wooCommerceSync';
import { WooCommerceService } from '../../../../services/woocommerce';
import { WooCommerceProduct, ProductWithImagePrefix } from '../models/article.model';

// Mock WooCommerce service
jest.mock('../../../../services/woocommerce');

describe('WooCommerce Sync', () => {
  let mockWooCommerceService: jest.Mocked<WooCommerceService>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockWooCommerceService = {
      getProducts: jest.fn(),
      createProduct: jest.fn(),
      updateProduct: jest.fn(),
      healthCheck: jest.fn(),
      getMediaByNamePrefix: jest.fn()
    } as any;
  });

  describe('upsertProductsToWooCommerce', () => {
    const mockProduct: WooCommerceProduct = {
      name: 'Test Product',
      sku: 'TEST-001',
      regular_price: '19.99',
      stock_quantity: 10,
      weight: '1.5',
      status: 'publish'
    };

    const mockProductWithImage: ProductWithImagePrefix = {
      product: mockProduct,
      imagePrefix: 'test-img'
    };

    it('should create new product when it does not exist', async () => {
      // Arrange
      mockWooCommerceService.getProducts.mockResolvedValue([]);
      mockWooCommerceService.createProduct.mockResolvedValue({
        id: 123,
        name: 'Test Product',
        sku: 'TEST-001'
      } as any);

      // Act
      const result = await upsertProductsToWooCommerce(
        mockWooCommerceService,
        [mockProductWithImage]
      );

      // Assert
      expect(result.totalProcessed).toBe(1);
      expect(result.created).toBe(1);
      expect(result.updated).toBe(0);
      expect(result.failed).toBe(0);
      expect(result.results[0]!.action).toBe('created');
      expect(result.results[0]!.productId).toBe(123);

      expect(mockWooCommerceService.getProducts).toHaveBeenCalledWith({ sku: 'TEST-001' });
      expect(mockWooCommerceService.getMediaByNamePrefix).toHaveBeenCalledWith('test-img');
      expect(mockWooCommerceService.createProduct).toHaveBeenCalledWith(mockProduct);
    });

    it('should update existing product', async () => {
      // Arrange
      const existingProduct = { id: 456, name: 'Existing Product', sku: 'TEST-001' };
      mockWooCommerceService.getProducts.mockResolvedValue([existingProduct] as any);
      mockWooCommerceService.updateProduct.mockResolvedValue({
        id: 456,
        name: 'Test Product',
        sku: 'TEST-001'
      } as any);
      mockWooCommerceService.getMediaByNamePrefix.mockResolvedValue([]);

      // Act
      const result = await upsertProductsToWooCommerce(
        mockWooCommerceService,
        [mockProductWithImage]
      );

      // Assert
      expect(result.totalProcessed).toBe(1);
      expect(result.created).toBe(0);
      expect(result.updated).toBe(1);
      expect(result.failed).toBe(0);
      expect(result.results[0]!.action).toBe('updated');
      expect(result.results[0]!.productId).toBe(456);

      expect(mockWooCommerceService.updateProduct).toHaveBeenCalledWith(456, mockProduct);
    });

    it('should skip existing product when configured', async () => {
      // Arrange
      const existingProduct = { id: 789, name: 'Existing Product', sku: 'TEST-001' };
      mockWooCommerceService.getProducts.mockResolvedValue([existingProduct] as any);
      mockWooCommerceService.getMediaByNamePrefix.mockResolvedValue([]);

      // Act
      const result = await upsertProductsToWooCommerce(
        mockWooCommerceService,
        [mockProductWithImage],
        { skipExistingProducts: true }
      );

      // Assert
      expect(result.totalProcessed).toBe(1);
      expect(result.created).toBe(0);
      expect(result.updated).toBe(0);
      expect(result.skipped).toBe(1);
      expect(result.failed).toBe(0);
      expect(result.results[0]!.action).toBe('skipped');
      expect(result.results[0]!.productId).toBe(789);

      expect(mockWooCommerceService.updateProduct).not.toHaveBeenCalled();
    });

    it('should handle validation errors', async () => {
      // Arrange
      const invalidProduct: WooCommerceProduct = {
        name: '', // Invalid: empty name
        sku: 'TEST-001',
        regular_price: '19.99'
      };

      const invalidProductWithImage: ProductWithImagePrefix = {
        product: invalidProduct,
        imagePrefix: 'test'
      };

      // Act
      const result = await upsertProductsToWooCommerce(
        mockWooCommerceService,
        [invalidProductWithImage],
        { validateBeforeUpsert: true }
      );

      // Assert
      expect(result.totalProcessed).toBe(1);
      expect(result.created).toBe(0);
      expect(result.updated).toBe(0);
      expect(result.failed).toBe(1);
      expect(result.results[0]!.action).toBe('failed');
      expect(result.results[0]!.error).toContain('Product name is required');

      expect(mockWooCommerceService.getProducts).not.toHaveBeenCalled();
    });

    it('should handle API errors gracefully', async () => {
      // Arrange
      mockWooCommerceService.getProducts.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await upsertProductsToWooCommerce(
        mockWooCommerceService,
        [mockProductWithImage]
      );

      // Assert
      expect(result.totalProcessed).toBe(1);
      expect(result.created).toBe(0);
      expect(result.updated).toBe(0);
      expect(result.failed).toBe(1);
      expect(result.results[0]!.action).toBe('failed');
      expect(result.results[0]!.error).toContain('API Error');
    });

    it('should process multiple products in batches', async () => {
      // Arrange
      const products: ProductWithImagePrefix[] = [
        { product: { name: 'Product 1', sku: 'TEST-001', regular_price: '10.00' }, imagePrefix: 'img1' },
        { product: { name: 'Product 2', sku: 'TEST-002', regular_price: '20.00' }, imagePrefix: 'img2' },
        { product: { name: 'Product 3', sku: 'TEST-003', regular_price: '30.00' }, imagePrefix: 'img3' }
      ];

      mockWooCommerceService.getProducts.mockResolvedValue([]);
      mockWooCommerceService.getMediaByNamePrefix.mockResolvedValue([]);
      mockWooCommerceService.createProduct.mockImplementation((product: any) =>
        Promise.resolve({ id: Math.random(), ...product })
      );

      // Act
      const result = await upsertProductsToWooCommerce(
        mockWooCommerceService,
        products,
        { batchSize: 2, delayBetweenBatches: 0 }
      );

      // Assert
      expect(result.totalProcessed).toBe(3);
      expect(result.created).toBe(3);
      expect(result.updated).toBe(0);
      expect(result.failed).toBe(0);
      expect(mockWooCommerceService.createProduct).toHaveBeenCalledTimes(3);
    });

    it('should assign images to product when image prefix matches', async () => {
      // Arrange
      const mockMediaItems = [
        {
          id: 1,
          source_url: 'https://example.com/test-img-1.jpg',
          alt_text: 'Test Image 1',
          title: { rendered: 'test-img-1' },
          slug: 'test-img-1'
        },
        {
          id: 2,
          source_url: 'https://example.com/test-img-2.jpg',
          alt_text: 'Test Image 2',
          title: { rendered: 'test-img-2' },
          slug: 'test-img-2'
        }
      ];

      mockWooCommerceService.getProducts.mockResolvedValue([]);
      mockWooCommerceService.getMediaByNamePrefix.mockResolvedValue(mockMediaItems as any);
      mockWooCommerceService.createProduct.mockResolvedValue({
        id: 123,
        name: 'Test Product',
        sku: 'TEST-001'
      } as any);

      // Act
      const result = await upsertProductsToWooCommerce(
        mockWooCommerceService,
        [mockProductWithImage]
      );

      // Assert
      expect(result.totalProcessed).toBe(1);
      expect(result.created).toBe(1);
      expect(result.failed).toBe(0);

      expect(mockWooCommerceService.getMediaByNamePrefix).toHaveBeenCalledWith('test-img');

      // Check that the product was created with images
      const createdProduct = mockWooCommerceService.createProduct.mock.calls[0]?.[0];
      expect(createdProduct).toBeDefined();
      expect(createdProduct?.images).toHaveLength(2);
      expect(createdProduct?.images?.[0]?.src).toBe('https://example.com/test-img-1.jpg');
      expect(createdProduct?.images?.[1]?.src).toBe('https://example.com/test-img-2.jpg');
    });

    it('should handle empty product array', async () => {
      // Act
      const result = await upsertProductsToWooCommerce(
        mockWooCommerceService,
        []
      );

      // Assert
      expect(result.totalProcessed).toBe(0);
      expect(result.created).toBe(0);
      expect(result.updated).toBe(0);
      expect(result.failed).toBe(0);
      expect(result.results).toHaveLength(0);

      expect(mockWooCommerceService.getProducts).not.toHaveBeenCalled();
    });

    it('should validate product fields correctly', async () => {
      // Arrange
      const invalidProducts: ProductWithImagePrefix[] = [
        { product: { name: '', sku: 'TEST-001' }, imagePrefix: '' }, // Empty name
        { product: { name: 'Valid Name', sku: '' }, imagePrefix: '' }, // Empty SKU
        { product: { name: 'Valid Name', sku: 'TEST-003', regular_price: 'invalid' }, imagePrefix: '' }, // Invalid price
        { product: { name: 'Valid Name', sku: 'TEST-004', stock_quantity: -1 }, imagePrefix: '' }, // Invalid stock
        { product: { name: 'Valid Name', sku: 'TEST-005', weight: 'invalid' }, imagePrefix: '' } // Invalid weight
      ];

      // Act
      const result = await upsertProductsToWooCommerce(
        mockWooCommerceService,
        invalidProducts,
        { validateBeforeUpsert: true }
      );

      // Assert
      expect(result.totalProcessed).toBe(5);
      expect(result.failed).toBe(5);
      expect(result.results[0]!.error).toContain('Product name is required');
      expect(result.results[1]!.error).toContain('Product SKU is required');
      expect(result.results[2]!.error).toContain('Invalid regular price format');
      expect(result.results[3]!.error).toContain('Invalid stock quantity');
      expect(result.results[4]!.error).toContain('Invalid weight format');
    });

    it('should handle mixed success and failure scenarios', async () => {
      // Arrange
      const products: ProductWithImagePrefix[] = [
        { product: { name: 'Valid Product', sku: 'TEST-001', regular_price: '10.00' }, imagePrefix: 'img1' },
        { product: { name: '', sku: 'TEST-002' }, imagePrefix: 'img2' }, // Invalid
        { product: { name: 'Another Valid', sku: 'TEST-003', regular_price: '30.00' }, imagePrefix: 'img3' }
      ];

      mockWooCommerceService.getProducts.mockImplementation((params: any) => {
        if (params.sku === 'TEST-001') return Promise.resolve([]);
        if (params.sku === 'TEST-003') return Promise.resolve([{ id: 999, name: 'Existing', sku: 'TEST-003' }]);
        return Promise.resolve([]);
      });

      mockWooCommerceService.getMediaByNamePrefix.mockResolvedValue([]);
      mockWooCommerceService.createProduct.mockResolvedValue({ id: 123 } as any);
      mockWooCommerceService.updateProduct.mockResolvedValue({ id: 999 } as any);

      // Act
      const result = await upsertProductsToWooCommerce(
        mockWooCommerceService,
        products,
        { validateBeforeUpsert: true }
      );

      // Assert
      expect(result.totalProcessed).toBe(3);
      expect(result.created).toBe(1);
      expect(result.updated).toBe(1);
      expect(result.failed).toBe(1);
      expect(result.results[0]!.action).toBe('created');
      expect(result.results[1]!.action).toBe('failed');
      expect(result.results[2]!.action).toBe('updated');
    });
  });

});
