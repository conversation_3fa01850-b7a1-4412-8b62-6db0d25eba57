/**
 * WooCommerce synchronization utilities for RZA data
 * Handles upserting products to WooCommerce via API
 */

import { WooCommerceService, Product } from '../../../services/woocommerce';
import { WooCommerceProduct, MappedWooCommerceProduct } from './models/article.model';
import { createModuleLogger } from '../../../infrastructure/logger';

const logger = createModuleLogger('rza-woocommerce-sync');

/**
 * Result of a single product upsert operation
 */
export interface ProductUpsertResult {
  sku: string;
  success: boolean;
  action: 'created' | 'updated' | 'skipped' | 'failed';
  productId?: number;
  error?: string;
}

/**
 * Result of batch product upsert operation
 */
export interface BatchUpsertResult {
  totalProcessed: number;
  created: number;
  updated: number;
  skipped: number;
  failed: number;
  results: ProductUpsertResult[];
  errors: string[];
}

/**
 * Configuration for product upsert operations
 */
export interface UpsertConfig {
  batchSize: number;
  delayBetweenBatches: number; // milliseconds
  skipExistingProducts: boolean;
  validateBeforeUpsert: boolean;
}

/**
 * Default upsert configuration
 */
export const DEFAULT_UPSERT_CONFIG: UpsertConfig = {
  batchSize: 10,
  delayBetweenBatches: 1000, // 1 second
  skipExistingProducts: false,
  validateBeforeUpsert: true
};

/**
 * Upserts multiple products to WooCommerce in batches with image assignment
 *
 * @param wooCommerceService - WooCommerce service instance
 * @param productsWithImages - Array of products with image prefixes to upsert
 * @param config - Upsert configuration
 * @returns Batch upsert result with statistics
 */
export async function upsertProductsToWooCommerce(
  wooCommerceService: WooCommerceService,
  productsWithImages: MappedWooCommerceProduct[],
  config: Partial<UpsertConfig> = {}
): Promise<BatchUpsertResult> {
  const upsertConfig = { ...DEFAULT_UPSERT_CONFIG, ...config };
  
  const result: BatchUpsertResult = {
    totalProcessed: 0,
    created: 0,
    updated: 0,
    skipped: 0,
    failed: 0,
    results: [],
    errors: []
  };

  if (productsWithImages.length === 0) {
    logger.info('No products to upsert');
    return result;
  }

  logger.info(`Starting batch upsert of ${productsWithImages.length} products`, {
    batchSize: upsertConfig.batchSize,
    delayBetweenBatches: upsertConfig.delayBetweenBatches
  });

  // Process products in batches
  for (let i = 0; i < productsWithImages.length; i += upsertConfig.batchSize) {
    const batch = productsWithImages.slice(i, i + upsertConfig.batchSize);
    const batchNumber = Math.floor(i / upsertConfig.batchSize) + 1;
    const totalBatches = Math.ceil(productsWithImages.length / upsertConfig.batchSize);

    logger.info(`Processing batch ${batchNumber}/${totalBatches} (${batch.length} products)`);

    // Process batch
    const batchResults = await processBatch(wooCommerceService, batch, upsertConfig);
    
    // Aggregate results
    result.results.push(...batchResults.results);
    result.errors.push(...batchResults.errors);
    result.totalProcessed += batchResults.totalProcessed;
    result.created += batchResults.created;
    result.updated += batchResults.updated;
    result.skipped += batchResults.skipped;
    result.failed += batchResults.failed;

    // Delay between batches (except for the last batch)
    if (i + upsertConfig.batchSize < productsWithImages.length && upsertConfig.delayBetweenBatches > 0) {
      logger.debug(`Waiting ${upsertConfig.delayBetweenBatches}ms before next batch`);
      await new Promise(resolve => setTimeout(resolve, upsertConfig.delayBetweenBatches));
    }
  }

  logger.info('Batch upsert completed', {
    totalProcessed: result.totalProcessed,
    created: result.created,
    updated: result.updated,
    skipped: result.skipped,
    failed: result.failed,
    errorCount: result.errors.length
  });

  return result;
}

/**
 * Processes a single batch of products with image assignment
 *
 * @param wooCommerceService - WooCommerce service instance
 * @param productsWithImages - Batch of products with image prefixes to process
 * @param config - Upsert configuration
 * @returns Batch result
 */
async function processBatch(
  wooCommerceService: WooCommerceService,
  productsWithImages: MappedWooCommerceProduct[],
  config: UpsertConfig
): Promise<BatchUpsertResult> {
  const result: BatchUpsertResult = {
    totalProcessed: 0,
    created: 0,
    updated: 0,
    skipped: 0,
    failed: 0,
    results: [],
    errors: []
  };

  // Process each product in the batch
  for (const productWithImage of productsWithImages) {
    try {
      const upsertResult = await upsertSingleProductWithImages(wooCommerceService, productWithImage, config);
      result.results.push(upsertResult);
      result.totalProcessed++;

      // Update counters
      switch (upsertResult.action) {
        case 'created':
          result.created++;
          break;
        case 'updated':
          result.updated++;
          break;
        case 'skipped':
          result.skipped++;
          break;
        case 'failed':
          result.failed++;
          if (upsertResult.error) {
            result.errors.push(`${upsertResult.sku}: ${upsertResult.error}`);
          }
          break;
      }
    } catch (error) {
      const errorMessage = `Unexpected error processing product ${productWithImage.product.sku}: ${error instanceof Error ? error.message : 'Unknown error'}`;
      logger.error(errorMessage);

      result.results.push({
        sku: productWithImage.product.sku || 'unknown',
        success: false,
        action: 'failed',
        error: errorMessage
      });
      result.totalProcessed++;
      result.failed++;
      result.errors.push(errorMessage);
    }
  }

  return result;
}

/**
 * Upserts a single product to WooCommerce with image assignment
 *
 * @param wooCommerceService - WooCommerce service instance
 * @param productWithImage - Product with image prefix to upsert
 * @param config - Upsert configuration
 * @returns Upsert result
 */
async function upsertSingleProductWithImages(
  wooCommerceService: WooCommerceService,
  productWithImage: MappedWooCommerceProduct,
  config: UpsertConfig
): Promise<ProductUpsertResult> {
  const { product, imagePrefix } = productWithImage;
  const sku = product.sku || 'unknown';

  try {
    // Validate product if required
    if (config.validateBeforeUpsert) {
      const validationError = validateProduct(product);
      if (validationError) {
        return {
          sku,
          success: false,
          action: 'failed',
          error: `Validation failed: ${validationError}`
        };
      }
    }

    // Assign images if imagePrefix is provided
    if (imagePrefix && imagePrefix.trim() !== '') {
      try {
        const matchingImages = await wooCommerceService.getMediaByNamePrefix(imagePrefix);
        if (matchingImages.length > 0) {
          // Convert media items to WooCommerce image format
          const productImages = matchingImages.map(media => ({
            src: media.source_url,
            alt: media.alt_text || media.title.rendered || ''
          }));

          // Assign images to product
          product.images = productImages;

          logger.debug(`Assigned ${productImages.length} images to product ${sku} with prefix: ${imagePrefix}`);
        } else {
          logger.debug(`No images found for product ${sku} with prefix: ${imagePrefix}`);
        }
      } catch (imageError) {
        logger.warn(`Failed to assign images for product ${sku} with prefix ${imagePrefix}:`, imageError);
        // Continue with product upsert even if image assignment fails
      }
    }

    // Check if product exists by SKU
    const existingProducts = await wooCommerceService.getProducts({ sku });
    const existingProduct = existingProducts.length > 0 ? existingProducts[0] : null;

    if (existingProduct) {
      // Product exists - update or skip
      if (config.skipExistingProducts) {
        logger.debug(`Skipping existing product: ${sku}`);
        const result: ProductUpsertResult = {
          sku,
          success: true,
          action: 'skipped'
        };
        if (existingProduct.id) {
          result.productId = existingProduct.id;
        }
        return result;
      }

      // Update existing product
      logger.debug(`Updating existing product: ${sku} (ID: ${existingProduct.id})`);
      const updatedProduct = await wooCommerceService.updateProduct(existingProduct.id!, product as Product);

      const result: ProductUpsertResult = {
        sku,
        success: true,
        action: 'updated'
      };
      if (updatedProduct.id) {
        result.productId = updatedProduct.id;
      }
      return result;
    } else {
      // Product doesn't exist - create new
      logger.debug(`Creating new product: ${sku}`);
      const createdProduct = await wooCommerceService.createProduct(product as Product);

      const result: ProductUpsertResult = {
        sku,
        success: true,
        action: 'created'
      };
      if (createdProduct.id) {
        result.productId = createdProduct.id;
      }
      return result;
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error(`Failed to upsert product ${sku}:`, error);
    
    return {
      sku,
      success: false,
      action: 'failed',
      error: errorMessage
    };
  }
}

/**
 * Validates a WooCommerce product before upsert
 * 
 * @param product - Product to validate
 * @returns Error message if validation fails, null if valid
 */
function validateProduct(product: WooCommerceProduct): string | null {
  // Check required fields
  if (!product.name || product.name.trim() === '') {
    return 'Product name is required';
  }

  if (!product.sku || product.sku.trim() === '') {
    return 'Product SKU is required';
  }

  // Validate price
  if (product.regular_price && isNaN(parseFloat(product.regular_price))) {
    return 'Invalid regular price format';
  }

  if (product.sale_price && isNaN(parseFloat(product.sale_price))) {
    return 'Invalid sale price format';
  }

  // Validate stock quantity
  if (product.stock_quantity !== undefined && (isNaN(product.stock_quantity) || product.stock_quantity < 0)) {
    return 'Invalid stock quantity';
  }

  // Validate weight
  if (product.weight && isNaN(parseFloat(product.weight))) {
    return 'Invalid weight format';
  }

  return null;
}
